<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة طلب #{{ order.id }} - {{ order.store.name }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            font-size: 10px;
            line-height: 1.2;
            color: #000;
            direction: rtl;
            text-align: right;
            background: white;
            margin: 10mm;
        }

        .print-container {
            max-width: 100%;
            margin: 0 auto;
        }

        /* Header Section */
        .print-header {
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
        }

        .company-info, .store-info {
            flex: 1;
            border: 1px solid #333;
            padding: 8px;
            background: #f8f9fa;
        }

        .company-logo {
            max-width: 60px;
            max-height: 45px;
            object-fit: contain;
            margin-bottom: 5px;
        }

        .company-name {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .section-title {
            font-size: 12px;
            font-weight: bold;
            margin-bottom: 5px;
            border-bottom: 1px solid #333;
            padding-bottom: 2px;
        }

        .info-line {
            margin-bottom: 2px;
            font-size: 10px;
        }

        .invoice-title {
            text-align: center;
            margin: 10px 0;
        }

        .invoice-title h1 {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 3px;
        }

        .invoice-dates {
            font-size: 10px;
            color: #666;
        }

        /* Order Summary */
        .order-summary {
            display: flex;
            justify-content: space-between;
            margin: 10px 0;
            padding: 8px;
            border: 1px solid #333;
            background: #f0f0f0;
        }

        .summary-item {
            text-align: center;
            flex: 1;
        }

        .summary-label {
            font-size: 9px;
            color: #666;
            margin-bottom: 2px;
        }

        .summary-value {
            font-size: 11px;
            font-weight: bold;
        }

        /* Items Table */
        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
            font-size: 10px;
        }

        .items-table th,
        .items-table td {
            border: 1px solid #333;
            padding: 4px;
            text-align: center;
        }

        .items-table th {
            background: #e0e0e0;
            font-weight: bold;
            font-size: 10px;
        }

        .product-name {
            text-align: right;
            max-width: 150px;
        }

        .product-details {
            font-size: 9px;
            color: #666;
            margin-top: 2px;
        }

        /* Totals Section */
        .totals-section {
            margin-top: 10px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 200px;
        }

        .totals-table td {
            border: 1px solid #333;
            padding: 4px 8px;
            font-size: 10px;
        }

        .totals-table .label {
            background: #f0f0f0;
            font-weight: bold;
            text-align: right;
        }

        .totals-table .value {
            text-align: left;
            min-width: 80px;
        }

        .total-final {
            background: #e0e0e0 !important;
            font-weight: bold;
            font-size: 11px;
        }

        /* Footer */
        .print-footer {
            margin-top: 20px;
            border-top: 1px solid #333;
            padding-top: 8px;
            display: flex;
            justify-content: space-between;
            font-size: 9px;
        }

        .footer-left, .footer-right {
            flex: 1;
        }

        .footer-right {
            text-align: left;
        }

        .footer-center {
            text-align: center;
            margin-top: 10px;
            font-weight: bold;
            font-size: 10px;
        }

        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 2px 6px;
            border: 1px solid #333;
            font-size: 9px;
            font-weight: bold;
        }

        /* Print optimizations */
        @media print {
            body {
                margin: 8mm;
                font-size: 9px;
            }

            .print-container {
                page-break-inside: avoid;
            }

            .items-table {
                page-break-inside: auto;
            }

            .items-table tr {
                page-break-inside: avoid;
            }

            .header-row {
                gap: 10px;
            }

            .company-info, .store-info {
                padding: 6px;
            }

            .info-line {
                margin-bottom: 1px;
                font-size: 8px;
            }
        }

        /* Compact spacing */
        .compact {
            margin: 2px 0;
        }

        .no-margin {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- Header Section -->
        <div class="print-header">
            <div class="header-row">
                <!-- Wholesaler Information -->
                <div class="company-info">
                    {% if wholesaler.logo %}
                    <img src="{{ wholesaler.logo.url }}" alt="{{ wholesaler.title }}" class="company-logo">
                    {% endif %}
                    <div class="company-name">{{ wholesaler.title }}</div>
                    <div class="info-line"><strong>المستخدم:</strong> {{ wholesaler.username }}</div>
                    <div class="info-line"><strong>الفئة:</strong> 
                        {% if wholesaler.category == 'GROCERY' %}بقالة
                        {% elif wholesaler.category == 'PHARMACEUTICAL' %}صيدلية
                        {% elif wholesaler.category == 'ELECTRONICS' %}إلكترونيات
                        {% else %}{{ wholesaler.category }}{% endif %}
                    </div>
                    {% if wholesaler.user.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ wholesaler.user.phone }}</div>
                    {% endif %}
                    {% if wholesaler.user.email %}
                    <div class="info-line"><strong>البريد:</strong> {{ wholesaler.user.email }}</div>
                    {% endif %}
                </div>

                <!-- Store Information -->
                <div class="store-info">
                    <div class="section-title">معلومات المتجر</div>
                    <div class="info-line"><strong>المتجر:</strong> {{ order.store.name }}</div>
                    <div class="info-line"><strong>المالك:</strong> {{ order.store.owner.username }}</div>
                    {% if order.store.owner.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ order.store.owner.phone }}</div>
                    {% endif %}
                    <div class="info-line"><strong>العنوان:</strong> {{ order.store.address }}</div>
                    {% if order.store.city %}
                    <div class="info-line"><strong>المدينة:</strong> {{ order.store.city.name }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Invoice Title -->
            <div class="invoice-title">
                <h1>فاتورة طلب #{{ order.id }}</h1>
                <div class="invoice-dates">
                    تاريخ الطلب: {{ order.created_at|date:"Y/m/d H:i" }} | 
                    تاريخ الطباعة: <span id="printDate"></span>
                </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <div class="summary-item">
                <div class="summary-label">رقم الطلب</div>
                <div class="summary-value">#{{ order.id }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">الحالة</div>
                <div class="summary-value">
                    <span class="status-badge">
                        {% if order.status == 'pending' %}في الانتظار
                        {% elif order.status == 'processing' %}قيد المعالجة
                        {% elif order.status == 'shipped' %}تم الشحن
                        {% elif order.status == 'delivered' %}تم التسليم
                        {% elif order.status == 'cancelled' %}ملغي
                        {% endif %}
                    </span>
                </div>
            </div>
            <div class="summary-item">
                <div class="summary-label">عدد المنتجات</div>
                <div class="summary-value">{{ order.products_total_quantity }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">المبلغ الإجمالي</div>
                <div class="summary-value">{{ order.total_price }} ج.م</div>
            </div>
        </div>

        <!-- Order Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">المنتج</th>
                    <th style="width: 15%;">الشركة</th>
                    <th style="width: 10%;">السعر</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order_items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="product-name">
                        <div>{{ item.product_item.product.name }}</div>
                        {% if item.product_item.product.barcode %}
                        <div class="product-details">{{ item.product_item.product.barcode }}</div>
                        {% endif %}
                    </td>
                    <td>
                        {% if item.product_item.product.company %}
                        {{ item.product_item.product.company.name }}
                        {% else %}
                        غير محدد
                        {% endif %}
                    </td>
                    <td>{{ item.price }} ج.م</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.total_price }} ج.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">المجموع الفرعي:</td>
                    <td class="value">{{ order.products_total_price }} ج.م</td>
                </tr>
                <tr>
                    <td class="label">الرسوم:</td>
                    <td class="value">{{ order.fees }} ج.م</td>
                </tr>
                <tr class="total-final">
                    <td class="label">المجموع الإجمالي:</td>
                    <td class="value">{{ order.total_price }} ج.م</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="print-footer">
            <div class="footer-left">
                <div><strong>{{ wholesaler.title }}</strong></div>
                {% if wholesaler.user.phone %}
                <div>الهاتف: {{ wholesaler.user.phone }}</div>
                {% endif %}
            </div>
            <div class="footer-right">
                <div>تم إنشاء الفاتورة إلكترونياً</div>
                <div>{{ order.created_at|date:"Y/m/d H:i" }}</div>
            </div>
        </div>
        
        <div class="footer-center">
            شكراً لتعاملكم معنا
        </div>
    </div>

    <script>
        // Set print date
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit'
        });

        // Auto-print when page loads
        window.onload = function() {
            window.print();
        };
    </script>
</body>
</html>
