<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة طلب #{{ order.id }} - {{ order.store.name }}</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary-green: #28a745;
            --secondary-green: #20c997;
            --accent-green: #198754;
            --light-green: #d4edda;
            --dark-green: #155724;
            --gray-100: #f8f9fa;
            --gray-200: #e9ecef;
            --gray-300: #dee2e6;
            --gray-600: #6c757d;
            --gray-800: #343a40;
            --gray-900: #212529;
            --border-color: #dee2e6;
            --text-primary: #212529;
            --text-secondary: #6c757d;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --info-color: #17a2b8;
            --danger-color: #dc3545;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: var(--text-primary);
            direction: rtl;
            text-align: right;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            margin: 10mm;
            font-weight: 400;
        }

        .print-container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            opacity: 0;
            transform: translateY(20px);
            animation: fadeInUp 0.6s ease-out forwards;
        }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* Header Section */
        .print-header {
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            padding: 15px;
            margin-bottom: 20px;
            position: relative;
        }

        .print-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 1;
        }

        .header-row {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            gap: 20px;
            margin-bottom: 15px;
        }

        .company-info, .store-info {
            flex: 1;
            background: rgba(255, 255, 255, 0.95);
            color: var(--text-primary);
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            padding: 12px;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .company-logo {
            max-width: 70px;
            max-height: 55px;
            object-fit: contain;
            margin-bottom: 8px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .company-name {
            font-size: 16px;
            font-weight: 700;
            margin-bottom: 6px;
            color: var(--primary-green);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .section-title {
            font-size: 13px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--primary-green);
            border-bottom: 2px solid var(--primary-green);
            padding-bottom: 3px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 30%;
            height: 2px;
            background: var(--secondary-green);
        }

        .info-line {
            margin-bottom: 3px;
            font-size: 10px;
            color: var(--text-secondary);
        }

        .info-line strong {
            color: var(--text-primary);
            font-weight: 600;
        }

        .invoice-title {
            text-align: center;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 8px;
            padding: 10px;
            margin: 15px 0 0 0;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .invoice-title h1 {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 5px;
            color: var(--primary-green);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        .invoice-dates {
            font-size: 11px;
            color: var(--text-secondary);
            font-weight: 500;
        }

        /* Order Summary */
        .order-summary {
            display: flex;
            justify-content: space-between;
            margin: 20px 15px;
            padding: 15px;
            background: linear-gradient(135deg, var(--light-green), rgba(255, 255, 255, 0.9));
            border: 2px solid var(--primary-green);
            border-radius: 10px;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.2);
        }

        .summary-item {
            text-align: center;
            flex: 1;
            position: relative;
        }

        .summary-item:not(:last-child)::after {
            content: '';
            position: absolute;
            right: -1px;
            top: 10%;
            height: 80%;
            width: 1px;
            background: var(--primary-green);
            opacity: 0.3;
        }

        .summary-label {
            font-size: 10px;
            color: var(--text-secondary);
            margin-bottom: 4px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .summary-value {
            font-size: 12px;
            font-weight: 700;
            color: var(--primary-green);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        }

        /* Items Table */
        .items-table {
            width: calc(100% - 30px);
            margin: 20px 15px;
            border-collapse: collapse;
            font-size: 10px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .items-table th,
        .items-table td {
            border: 1px solid var(--border-color);
            padding: 8px 6px;
            text-align: center;
        }

        .items-table th {
            background: linear-gradient(135deg, var(--primary-green), var(--accent-green));
            color: white;
            font-weight: 600;
            font-size: 10px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            position: relative;
        }

        .items-table th::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 2px;
            background: var(--secondary-green);
        }

        .items-table tbody tr:nth-child(even) {
            background: var(--gray-100);
        }

        .items-table tbody tr:hover {
            background: var(--light-green);
            transition: background-color 0.2s ease;
        }

        .product-name {
            text-align: right;
            max-width: 150px;
            font-weight: 500;
        }

        .product-details {
            font-size: 9px;
            color: var(--text-secondary);
            margin-top: 2px;
            font-style: italic;
        }

        /* Totals Section */
        .totals-section {
            margin: 20px 15px;
            display: flex;
            justify-content: flex-end;
        }

        .totals-table {
            border-collapse: collapse;
            min-width: 250px;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        }

        .totals-table td {
            border: 1px solid var(--border-color);
            padding: 8px 12px;
            font-size: 11px;
        }

        .totals-table .label {
            background: linear-gradient(135deg, var(--gray-100), var(--gray-200));
            font-weight: 600;
            text-align: right;
            color: var(--text-primary);
        }

        .totals-table .value {
            text-align: left;
            min-width: 100px;
            font-weight: 500;
            color: var(--text-secondary);
        }

        .total-final .label {
            background: linear-gradient(135deg, var(--primary-green), var(--accent-green)) !important;
            color: white !important;
            font-weight: 700;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .total-final .value {
            background: var(--light-green) !important;
            color: var(--dark-green) !important;
            font-weight: 700;
            font-size: 12px;
        }

        /* Footer */
        .print-footer {
            margin: 25px 15px 15px 15px;
            background: linear-gradient(135deg, var(--gray-100), rgba(255, 255, 255, 0.9));
            border: 2px solid var(--border-color);
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .footer-left, .footer-right {
            flex: 1;
        }

        .footer-left {
            color: var(--text-primary);
            font-weight: 500;
        }

        .footer-right {
            text-align: left;
            color: var(--text-secondary);
            font-size: 9px;
        }

        .footer-center {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: linear-gradient(135deg, var(--primary-green), var(--secondary-green));
            color: white;
            border-radius: 6px;
            font-weight: 600;
            font-size: 12px;
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
            box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
        }

        /* Status Badge */
        .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 9px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .status-pending { background: var(--warning-color); color: var(--gray-800); }
        .status-processing { background: var(--info-color); color: white; }
        .status-shipped { background: var(--primary-green); color: white; }
        .status-delivered { background: var(--success-color); color: white; }
        .status-cancelled { background: var(--danger-color); color: white; }

        /* Print optimizations */
        @media print {
            body {
                margin: 8mm;
                font-size: 9px;
                background: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .print-container {
                page-break-inside: avoid;
                box-shadow: none !important;
                border-radius: 0 !important;
                animation: none !important;
                opacity: 1 !important;
                transform: none !important;
            }

            .print-header {
                background: var(--primary-green) !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .items-table {
                page-break-inside: auto;
                box-shadow: none !important;
            }

            .items-table tr {
                page-break-inside: avoid;
            }

            .items-table th {
                background: var(--primary-green) !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .header-row {
                gap: 10px;
            }

            .company-info, .store-info {
                padding: 8px;
                background: var(--gray-100) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .info-line {
                margin-bottom: 1px;
                font-size: 8px;
            }

            .order-summary {
                background: var(--light-green) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .totals-table .total-final .label {
                background: var(--primary-green) !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .totals-table .total-final .value {
                background: var(--light-green) !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .footer-center {
                background: var(--primary-green) !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }

            .status-badge {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
            }
        }

        /* Compact spacing */
        .compact {
            margin: 2px 0;
        }

        .no-margin {
            margin: 0;
        }
    </style>
</head>
<body>
    <div class="print-container">
        <!-- Header Section -->
        <div class="print-header">
            <div class="header-content">
            <div class="header-row">
                <!-- Wholesaler Information -->
                <div class="company-info">
                    {% if wholesaler.logo %}
                    <img src="{{ wholesaler.logo.url }}" alt="{{ wholesaler.title }}" class="company-logo">
                    {% endif %}
                    <div class="company-name">{{ wholesaler.title }}</div>
                    <div class="info-line"><strong>المستخدم:</strong> {{ wholesaler.username }}</div>
                    <div class="info-line"><strong>الفئة:</strong> 
                        {% if wholesaler.category == 'GROCERY' %}بقالة
                        {% elif wholesaler.category == 'PHARMACEUTICAL' %}صيدلية
                        {% elif wholesaler.category == 'ELECTRONICS' %}إلكترونيات
                        {% else %}{{ wholesaler.category }}{% endif %}
                    </div>
                    {% if wholesaler.user.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ wholesaler.user.phone }}</div>
                    {% endif %}
                    {% if wholesaler.user.email %}
                    <div class="info-line"><strong>البريد:</strong> {{ wholesaler.user.email }}</div>
                    {% endif %}
                </div>

                <!-- Store Information -->
                <div class="store-info">
                    <div class="section-title">معلومات المتجر</div>
                    <div class="info-line"><strong>المتجر:</strong> {{ order.store.name }}</div>
                    <div class="info-line"><strong>المالك:</strong> {{ order.store.owner.username }}</div>
                    {% if order.store.owner.phone %}
                    <div class="info-line"><strong>الهاتف:</strong> {{ order.store.owner.phone }}</div>
                    {% endif %}
                    <div class="info-line"><strong>العنوان:</strong> {{ order.store.address }}</div>
                    {% if order.store.city %}
                    <div class="info-line"><strong>المدينة:</strong> {{ order.store.city.name }}</div>
                    {% endif %}
                </div>
            </div>

            <!-- Invoice Title -->
            <div class="invoice-title">
                <h1>فاتورة طلب #{{ order.id }}</h1>
                <div class="invoice-dates">
                    تاريخ الطلب: {{ order.created_at|date:"Y/m/d H:i" }} |
                    تاريخ الطباعة: <span id="printDate"></span>
                </div>
            </div>
            </div>
        </div>

        <!-- Order Summary -->
        <div class="order-summary">
            <div class="summary-item">
                <div class="summary-label">رقم الطلب</div>
                <div class="summary-value">#{{ order.id }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">الحالة</div>
                <div class="summary-value">
                    <span class="status-badge
                        {% if order.status == 'pending' %}status-pending
                        {% elif order.status == 'processing' %}status-processing
                        {% elif order.status == 'shipped' %}status-shipped
                        {% elif order.status == 'delivered' %}status-delivered
                        {% elif order.status == 'cancelled' %}status-cancelled
                        {% endif %}">
                        {% if order.status == 'pending' %}في الانتظار
                        {% elif order.status == 'processing' %}قيد المعالجة
                        {% elif order.status == 'shipped' %}تم الشحن
                        {% elif order.status == 'delivered' %}تم التسليم
                        {% elif order.status == 'cancelled' %}ملغي
                        {% endif %}
                    </span>
                </div>
            </div>
            <div class="summary-item">
                <div class="summary-label">عدد المنتجات</div>
                <div class="summary-value">{{ order.products_total_quantity }}</div>
            </div>
            <div class="summary-item">
                <div class="summary-label">المبلغ الإجمالي</div>
                <div class="summary-value">{{ order.total_price }} ج.م</div>
            </div>
        </div>

        <!-- Order Items Table -->
        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">#</th>
                    <th style="width: 35%;">المنتج</th>
                    <th style="width: 15%;">الشركة</th>
                    <th style="width: 10%;">السعر</th>
                    <th style="width: 10%;">الكمية</th>
                    <th style="width: 15%;">الإجمالي</th>
                </tr>
            </thead>
            <tbody>
                {% for item in order_items %}
                <tr>
                    <td>{{ forloop.counter }}</td>
                    <td class="product-name">
                        <div>{{ item.product_item.product.name }}</div>
                        {% if item.product_item.product.barcode %}
                        <div class="product-details">{{ item.product_item.product.barcode }}</div>
                        {% endif %}
                    </td>
                    <td>
                        {% if item.product_item.product.company %}
                        {{ item.product_item.product.company.name }}
                        {% else %}
                        غير محدد
                        {% endif %}
                    </td>
                    <td>{{ item.price }} ج.م</td>
                    <td>{{ item.quantity }}</td>
                    <td>{{ item.total_price }} ج.م</td>
                </tr>
                {% endfor %}
            </tbody>
        </table>

        <!-- Totals Section -->
        <div class="totals-section">
            <table class="totals-table">
                <tr>
                    <td class="label">المجموع الفرعي:</td>
                    <td class="value">{{ order.products_total_price }} ج.م</td>
                </tr>
                <tr>
                    <td class="label">الرسوم:</td>
                    <td class="value">{{ order.fees }} ج.م</td>
                </tr>
                <tr class="total-final">
                    <td class="label">المجموع الإجمالي:</td>
                    <td class="value">{{ order.total_price }} ج.م</td>
                </tr>
            </table>
        </div>

        <!-- Footer -->
        <div class="print-footer">
            <div class="footer-left">
                <div><strong>{{ wholesaler.title }}</strong></div>
                {% if wholesaler.user.phone %}
                <div>الهاتف: {{ wholesaler.user.phone }}</div>
                {% endif %}
            </div>
            <div class="footer-right">
                <div>تم إنشاء الفاتورة إلكترونياً</div>
                <div>{{ order.created_at|date:"Y/m/d H:i" }}</div>
            </div>
        </div>
        
        <div class="footer-center">
            شكراً لتعاملكم معنا
        </div>
    </div>

    <script>
        // Set print date with enhanced formatting
        document.getElementById('printDate').textContent = new Date().toLocaleDateString('ar-EG', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            timeZone: 'Africa/Cairo'
        });

        // Enhanced auto-print and return functionality
        let printDialogHandled = false;

        function handlePrintCompletion() {
            if (printDialogHandled) return;
            printDialogHandled = true;

            // Add a small delay to ensure print dialog has closed
            setTimeout(() => {
                // Check if this window was opened by another window (print popup)
                if (window.opener && !window.opener.closed) {
                    // Focus back to the parent window
                    window.opener.focus();
                    // Close this print window
                    window.close();
                } else {
                    // If not a popup, try to go back in history
                    if (window.history.length > 1) {
                        window.history.back();
                    } else {
                        // Fallback: redirect to orders list
                        window.location.href = '/wholesaler/orders/';
                    }
                }
            }, 500);
        }

        // Auto-print when page loads
        window.onload = function() {
            // Small delay to ensure page is fully rendered
            setTimeout(() => {
                window.print();
            }, 100);
        };

        // Handle print dialog events
        window.addEventListener('beforeprint', function() {
            console.log('Print dialog opened');
        });

        window.addEventListener('afterprint', function() {
            console.log('Print dialog closed');
            handlePrintCompletion();
        });

        // Fallback for browsers that don't support afterprint
        // Monitor for focus return which often indicates print dialog closed
        let focusTimeout;
        window.addEventListener('focus', function() {
            clearTimeout(focusTimeout);
            focusTimeout = setTimeout(() => {
                if (!printDialogHandled) {
                    handlePrintCompletion();
                }
            }, 1000);
        });

        // Handle escape key to close window
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                handlePrintCompletion();
            }
        });

        // Fallback timeout - auto-close after 30 seconds if no interaction
        setTimeout(() => {
            if (!printDialogHandled) {
                console.log('Auto-closing print window after timeout');
                handlePrintCompletion();
            }
        }, 30000);
    </script>
</body>
</html>
