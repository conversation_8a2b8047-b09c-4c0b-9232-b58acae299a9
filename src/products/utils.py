import openai
from django.conf import settings
import logging
from django.contrib.postgres.search import SearchVector
import time
import random

logger = logging.getLogger(__name__)

# Configure OpenAI API
openai.api_key = settings.OPENAI_API_KEY


def generate_search_vector(product):
    """
    Generate a search vector for the given product.
    """
    # example Article.objects.update(
    # search_vector=SearchVector('title', weight='A') + SearchVector('content', weight='B'))
    product.search_vector = (
        SearchVector("name", weight="A", config="arabic")
        + SearchVector("title", weight="A", config="arabic")
        + SearchVector("description", weight="B", config="arabic")
        + SearchVector("company__name", weight="C", config="arabic")
        + SearchVector("category__name", weight="C", config="arabic")
    )
    return product


def generate_embedding(text, dimensions=384):
    """
    Generate an embedding for the given text using OpenAI's API.

    Args:
        text (str): The text to generate an embedding for.
        dimensions (int): The number of dimensions for the embedding.

    Returns:
        list: The embedding vector.
    """
    try:
        response = openai.embeddings.create(
            model="text-embedding-3-large",
            input=text,
            dimensions=dimensions,
        )
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        return None


def generate_embeddings_bulk(texts, dimensions=384, max_retries=3, base_delay=1.0):
    """
    Generate embeddings for a list of texts using OpenAI's API with rate limiting and retry logic.

    Args:
        texts (list): The list of texts to generate embeddings for.
        dimensions (int): The number of dimensions for the embedding.
        max_retries (int): Maximum number of retry attempts.
        base_delay (float): Base delay in seconds for exponential backoff.

    Returns:
        list: List of embedding data objects or None if failed.
    """
    for attempt in range(max_retries + 1):
        try:
            response = openai.embeddings.create(
                model="text-embedding-3-large",
                input=texts,
                dimensions=dimensions,
            )
            return response.data
        except openai.RateLimitError as e:
            if attempt < max_retries:
                # Exponential backoff with jitter
                delay = base_delay * (2**attempt) + random.uniform(0, 1)
                logger.warning(
                    f"Rate limit hit, retrying in {delay:.2f} seconds (attempt {attempt + 1}/{max_retries + 1})"
                )
                time.sleep(delay)
                continue
            else:
                logger.error(
                    f"Rate limit exceeded after {max_retries + 1} attempts: {str(e)}"
                )
                return None
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            return None

    return None
